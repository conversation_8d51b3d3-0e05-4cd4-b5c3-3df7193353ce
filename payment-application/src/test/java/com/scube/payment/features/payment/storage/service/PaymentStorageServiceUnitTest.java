package com.scube.payment.features.payment.storage.service;

import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.mapper.PaymentMapper;
import com.scube.payment.features.payment.processing.mapper.RefundMapper;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.repository.PaymentStorageRepository;
import com.scube.payment.features.payment.storage.repository.RefundTransactionStorageRepository;
import com.scube.payment.util.Mocks;
import com.scube.payment.util.TestPaymentMapperImpl;
import com.scube.rabbit.core.AmqpGateway;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class PaymentStorageServiceUnitTest {
    private PaymentStorageService paymentStorageService;

    @Mock
    private PaymentStorageRepository paymentStorageRepository;

    private final PaymentMapper paymentMapper = new TestPaymentMapperImpl();

    @Mock
    private AmqpGateway amqpGateway;

    @Mock
    private RefundTransactionStorageRepository refundTransactionRepository;

    @Mock
    private RefundMapper refundMapper;

    @BeforeEach
    public void setUp() {
        paymentStorageService = new PaymentStorageService(paymentStorageRepository, refundTransactionRepository, paymentMapper, amqpGateway, refundMapper);
    }

    @Test
    public void testStorePayment() {
        SubmitPaymentRequestDto paymentRequest = Mocks.createMockSubmitPaymentRequestDto();
        paymentRequest.setOrderId(UUID.randomUUID());
        paymentRequest.setPaymentAmount(new BigDecimal("100.00"));
        paymentRequest.setTransactionDate(Instant.now());

        Payment expectedPayment = paymentMapper.toEntity(paymentRequest);
        expectedPayment.setStatus(PaymentStatus.SUCCESS);
        expectedPayment.setTransactionId(UUID.randomUUID().toString());

        when(paymentStorageRepository.save(any(Payment.class))).thenReturn(expectedPayment);

        Payment actualPayment = paymentStorageService.storePayment(paymentRequest);

        verify(paymentStorageRepository).save(any(Payment.class));
        assertEquals(expectedPayment, actualPayment);
    }

    @Test
    public void testFinalizePayment() {
        long paymentId = 1L;
        Payment payment = new Payment();
        payment.setStatus(PaymentStatus.SUCCESS);
        payment.setTransactionDate(Instant.now());

        when(paymentStorageRepository.findById(paymentId)).thenReturn(java.util.Optional.of(payment));
        when(paymentStorageRepository.save(any(Payment.class))).thenReturn(payment);

        paymentStorageService.finalizePayment(paymentId);

        verify(paymentStorageRepository).findById(paymentId);
        verify(paymentStorageRepository).save(any(Payment.class));
    }

    @Test
    public void testFinalizePayment_paymentNotFound() {
        long paymentId = 1L;
        when(paymentStorageRepository.findById(paymentId)).thenReturn(java.util.Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
            paymentStorageService.finalizePayment(paymentId);
        });

        verify(paymentStorageRepository).findById(paymentId);
        assertEquals(HttpStatus.NOT_FOUND.value(), exception.getStatusCode().value());
    }

    @Test
    public void testGetPayments() {
        List<Payment> payments = new ArrayList<>();
        when(paymentStorageRepository.findAll()).thenReturn(payments);

        List<Payment> actualPayments = paymentStorageService.getPaymentResponseDtos();

        verify(paymentStorageRepository).findAll();
        assertEquals(payments, actualPayments);
    }

    @Test
    public void testDeletePayment() {
        long paymentId = 1L;
        paymentStorageService.deletePayment(paymentId);
        verify(paymentStorageRepository).deleteById(paymentId);
    }

    @Test
    public void testGetPaymentsByOrderId() {
        UUID orderId = UUID.randomUUID();
        List<Payment> payments = new ArrayList<>();
        when(paymentStorageRepository.findByOrderIdOrderByCreatedDateDesc(orderId)).thenReturn(payments);

        List<GetPaymentResponseDto> actualPayments = paymentStorageService.getPaymentResponseDtos(orderId);

        verify(paymentStorageRepository).findByOrderIdOrderByCreatedDateDesc(orderId);
        assertEquals(payments.stream().map(GetPaymentResponseDto::new).toList(), actualPayments);
    }

    @Test
    public void testStoreReceiptId() {
        UUID paymentId = UUID.randomUUID();
        UUID receiptId = UUID.randomUUID();
        Payment payment = new Payment();
        when(paymentStorageRepository.findByUuidOrThrow(paymentId)).thenReturn(payment);
        when(paymentStorageRepository.save(any(Payment.class))).thenReturn(payment);

        paymentStorageService.storeReceiptId(paymentId, receiptId);

        verify(paymentStorageRepository).findByUuidOrThrow(paymentId);
        verify(paymentStorageRepository).save(any(Payment.class));
    }
}