package com.scube.payment.features.providers.mock;

import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.providers.gateway.IPaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import com.scube.payment.features.webhook.IWebhookPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("mockPaymentProviderGateway")
@Slf4j
@Profile("test")
public class MockPaymentProviderGateway implements IPaymentProviderGateway<
        PaymentTokenRequest,
        PaymentTokenResponse,
        MockPaymentProviderGateway.MockWebhookPayload
        > {
    @Override
    public PaymentTokenResponse getToken(PaymentTokenRequest req) {
        log.debug("Mocking token request");
        return new PaymentTokenResponse("mock-token", "ref-id", "Mock Provider", Map.of());
    }

    @Override
    public void createRefund(RefundTransaction refundTransaction) {
        log.debug("Mocking create refund request");
    }

    @Override
    public void authCapture(MockWebhookPayload request) {
        log.debug("Mocking auth capture request");
    }

    @Override
    public void settle(MockWebhookPayload request) {
        log.debug("Mocking settle request");
    }

    @Override
    public void refund(MockWebhookPayload request) {
        log.debug("Mocking refund request");
    }

    @Override
    public void refundFailed(MockWebhookPayload request) {
        log.debug("Mocking refund failed request");
    }

    @Override
    public void _void(MockWebhookPayload request) {log.debug("Mocking void request");}

    public class MockWebhookPayload implements IWebhookPayload {}
}
