package com.scube.payment.features.providers.elavon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "payment-providers.elavon")
public class ElavonProperties {
    public static final String PAYMENT_PROVIDER_NAME = "Elavon";

    private String url;
    private String virtualMerchantProcessUrl;
    private String accountId;
    private String userId;
    private String pin;
    private String transactionType = "ccsale";
    private String transactionCurrency = "USD";
    // for the webhook
    private String webhookConfirmationText = "ClerkXpress Payment Confirmed";
    private String webhookUsername;
    private String webhookPassword;
}