package com.scube.payment.features.payment.receipts.rabbit;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GenerateDocumentCommand implements IRabbitFanoutPublisher {
    private String parentId;
    private String parentType;
    private String groupId;
    private String nameKey;
    private JsonNode data;
}

