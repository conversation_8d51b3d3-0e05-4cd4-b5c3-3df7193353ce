package com.scube.payment.features.webhook.inbox.service;

import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.payment.features.webhook.inbox.model.InboxStatus;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.repo.WebhookInboxRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MarkerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class WebhookInboxProcessingService {
    private final Map<String, IPaymentProviderWebhookService> webhookServices;
    private final WebhookInboxRepository webhookInboxRepository;

    @Transactional
    public void processWebhook(UUID webhookInboxUuid) {
        WebhookInbox webhookInbox = webhookInboxRepository.findByUuid(webhookInboxUuid).orElseThrow(() -> new ResponseStatusException(
                org.springframework.http.HttpStatus.NOT_FOUND, "Webhook inbox entry not found"));

        if (webhookInbox.isCompleted()) {
            throw new ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "Webhook inbox entry already processed");
        }

        processWebhook(webhookInbox);
    }

    @Transactional
    public void processWebhook(WebhookInbox webhookInbox) {
        PaymentDetailsEvent details = null;
        try {
            IPaymentProviderWebhookService webhookService = webhookServices.get(webhookInbox.getPaymentProvider());
            details = webhookService.getPaymentDetails(webhookInbox);

            webhookService.process(webhookInbox);
            webhookInbox.markCompleted(details);
        } catch (Exception e) {
            log.error(MarkerFactory.getMarker("CRITICAL"),
                    "Failed to process webhook " + webhookInbox.getUuid(), e);
            webhookInbox.markFailed(details);
        }
        webhookInboxRepository.save(webhookInbox);
    }

    public void processPending() {
        webhookInboxRepository.findByStatus(InboxStatus.PENDING)
                .forEach(this::processWebhook);
    }
}
