package com.scube.payment.features.payment.storage.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.rabbit.PaymentTotalsByDateQueryListener;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PaymentStorageRepository extends AuditableEntityRepository<Payment, Long> {
    @Query(nativeQuery = true, value = """
            SELECT
                SUM(CASE WHEN payment_type = 'card' THEN amount ELSE 0 END) AS "cardTotal",
                SUM(CASE WHEN payment_type = 'check' THEN amount ELSE 0 END) AS "checkTotal",
            	SUM(CASE WHEN payment_type = 'cash' THEN amount ELSE 0 END) AS "cashTotal",
            	SUM(amount) AS total
            FROM payment
            WHERE transaction_date BETWEEN :startDate AND :endDate
            """)
    PaymentTotalsByDateQueryListener.PaymentTotalsProjection getTotalPaymentsByDate(LocalDate startDate, LocalDate endDate);

    List<Payment> findByOrderIdOrderByCreatedDateDesc(UUID orderId);

    boolean existsByPaymentReference(@Size(max = 255) String paymentReference);

    Optional<Payment> findByPaymentReference(@Size(max = 255) String paymentReference);
}