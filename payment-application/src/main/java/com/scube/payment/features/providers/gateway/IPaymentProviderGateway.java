package com.scube.payment.features.providers.gateway;

import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.webhook.IWebhookPayload;

public interface IPaymentProviderGateway<
        TTokenRequest extends PaymentTokenRequest,
        TTokenResponse extends PaymentTokenResponse,
        TWebhookPayload extends IWebhookPayload> {
    TTokenResponse getToken(TTokenRequest request);
    void createRefund(RefundTransaction refundTransaction);
    void authCapture(TWebhookPayload request);
    void settle(TWebhookPayload request);
    void refund(TWebhookPayload request);
    void refundFailed(TWebhookPayload request);
    void _void(TWebhookPayload request);
}
