package com.scube.payment.features.providers.mock;

import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;

@Profile("test")
@Slf4j
public class MockPaymentProviderWebhookService implements IPaymentProviderWebhookService<MockPaymentProviderGateway.MockWebhookPayload> {
    @Override
    public void store(MockPaymentProviderGateway.MockWebhookPayload payload) {
        log.debug("Mocking storing payload");
    }

    @Override
    public void process(WebhookInbox inbox) {
        log.debug("Mocking processing inbox");
    }

    @Override
    public PaymentDetailsEvent getPaymentDetails(WebhookInbox inbox) {
        return new PaymentDetailsEvent();
    }
}
