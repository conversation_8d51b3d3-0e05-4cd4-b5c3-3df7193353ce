package com.scube.payment.features.providers.elavon;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.payment.features.webhook.IWebhookPayload;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ElavonEvent implements IWebhookPayload, Serializable {
    @JsonAlias({"orderId", "order_id"})
    private String orderId;

    @JsonProperty("ssl_merchant_initiated_unscheduled")
    private String merchantInitiatedUnscheduled;

    @JsonProperty("ssl_last_name")
    private String lastName;

    @JsonProperty("ssl_partner_app_id")
    private String partnerAppId;

    @JsonProperty("ssl_card_number")
    private String cardNumber;

    @JsonProperty("ssl_oar_data")
    private String oarData;

    @JsonProperty("ssl_result")
    private String result;

    @JsonProperty("ssl_txn_id")
    private String txnId;

    @JsonProperty("ssl_avs_response")
    private String avsResponse;

    @JsonProperty("ssl_approval_code")
    private String approvalCode;

    @JsonProperty("ssl_email")
    private String email;

    @JsonProperty("ssl_phone")
    private String phone;

    @JsonProperty("ssl_amount")
    private BigDecimal amount;

    @JsonProperty("ssl_transaction_currency")
    private String transactionCurrency;

    @JsonProperty("ssl_txn_time")
    private String txnTime;

    @JsonProperty("ssl_tid")
    private String tid;

    @JsonProperty("ssl_exp_date")
    private String expDate;

    @JsonProperty("ssl_card_short_description")
    private String cardShortDescription;

    @JsonProperty("ssl_get_token")
    private String getToken;

    @JsonProperty("ssl_card_type")
    private String cardType;

    @JsonProperty("ssl_merchant_id")
    private String merchantId;

    @JsonProperty("ssl_association_token_data")
    private String associationTokenData;

    @JsonProperty("ssl_transaction_type")
    private String transactionType;

    @JsonProperty("ssl_avs_address")
    private String address;

    @JsonProperty("ssl_address2")
    private String address2;

    @JsonProperty("ssl_state")
    private String state;

    @JsonProperty("ssl_city")
    private String city;

    @JsonProperty("ssl_avs_zip")
    private String zip;

    @JsonProperty("ssl_account_balance")
    private String accountBalance;

    @JsonProperty("ssl_ps2000_data")
    private String ps2000Data;

    @JsonProperty("ssl_result_message")
    private String resultMessage;

    @JsonProperty("ssl_first_name")
    private String firstName;

    @JsonProperty("ssl_cvv2_response")
    private String cvv2Response;

    @JsonProperty("ssl_user_id")
    private String userId;

    @JsonProperty("ssl_ref_txn_id")
    private String originalTransactionId;

    @JsonProperty("ssl_refund_amount")
    private BigDecimal refundAmount;

    @JsonProperty("ssl_refund_reason")
    private String refundReason;

    public String getId() {
        return txnId;
    }

    public String getName() {
        return String.format("%s %s", firstName, lastName);
    }

}