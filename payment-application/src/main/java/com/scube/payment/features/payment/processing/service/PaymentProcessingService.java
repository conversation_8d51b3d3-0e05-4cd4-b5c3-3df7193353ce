package com.scube.payment.features.payment.processing.service;

import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentProcessingService {
    private final AmqpGateway amqpGateway;
    private final PaymentStorageService paymentStorageService;


    @Transactional
    public SubmitPaymentResponseDto submitPayment(SubmitPaymentRequestDto paymentRequest) {
        log.debug("Processing payment request for order {}", paymentRequest.getOrderId());

        var orderId = paymentRequest.getOrderId();

        List<Payment> existingPayments = paymentStorageService.getPayments(orderId);

        BigDecimal totalPaid = existingPayments.stream().map(Payment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal balance = paymentRequest.getOrderAmount().subtract(totalPaid);

        if (balance.compareTo(paymentRequest.getPaymentAmount()) < 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Payment amount exceeds balance of " + balance);
        }

        Payment payment = paymentStorageService.storePayment(paymentRequest);

        if (balance.compareTo(paymentRequest.getPaymentAmount()) == 0) {
            amqpGateway.publish(new OrderPaymentCompletedEvent(orderId.toString()));
        } else {
            amqpGateway.publish(new OrderSubmitPaymentEvent(orderId.toString()));
        }

        balance = balance.subtract(paymentRequest.getPaymentAmount());

        return new SubmitPaymentResponseDto(payment.getStatus().getKey(), payment.getUuid(), orderId, balance);
    }

    public record OrderPaymentCompletedEvent(String orderId) implements IRabbitFanoutPublisher {}
    public record OrderSubmitPaymentEvent(String orderId) implements IRabbitFanoutPublisher {}
}
