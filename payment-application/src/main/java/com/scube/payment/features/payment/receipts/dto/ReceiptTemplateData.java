package com.scube.payment.features.payment.receipts.dto;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.scube.payment.features.payment.processing.dto.PayeeDto;
import com.scube.payment.features.tenant.TenantDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ReceiptTemplateData {
    private String orderNumber;
    private String orderDate;
    private String orderTime;
    private String clerkName;
    private String clerkTitle;
    private List<ReceiptTemplateData.ReceiptItem> receiptItems;
    private String subTotal;
    private String discounts;
    private String total;
    private String balance;
    private String paymentType;
    private List<Payee> payee;
    private String notes;
    private String paymentReference;
    private TenantDto tenant;

    @JsonUnwrapped
    private ReceiptTemplateData.Municipality municipality;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Payee {
        private String name;
        private String billingAddress;
        private String billingCityStateZip;

        public static Payee of(PayeeDto payeeDto) {
            return Payee.builder()
                    .name(payeeDto.getFullName())
                    .billingAddress(payeeDto.getBillingAddressLine1Line2())
                    .billingCityStateZip(payeeDto.getBillingCityStateZip())
                    .build();
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReceiptItem {
        private String item;
        private String unitPrice;
        private String note;
        private String userId;
        private String quantity;
        private String amount;

        public void combine(ReceiptItem other) {
            int combinedQuantity = Integer.parseInt(this.quantity) + Integer.parseInt(other.quantity);
            BigDecimal combinedAmount = new BigDecimal(this.amount).add(new BigDecimal(other.amount));
            this.quantity = String.valueOf(combinedQuantity);
            this.amount = combinedAmount.toString();
        }
    }

    /**
     * Loops through a list of ReceiptItems and combines duplicates into a single ReceiptItem
     * with an updated quantity and amount.
     */
    public static List<ReceiptItem> combineDuplicateItems(List<ReceiptItem> receiptItems) {
        Map<String, ReceiptItem> map = new HashMap<>();

        for (ReceiptItem item : receiptItems) {
            ReceiptItem existingItem = map.get(item.getItem());
            if (existingItem == null) {
                map.put(item.getItem(), item);
            } else {
                existingItem.combine(item);
                map.put(item.getItem(), existingItem);
            }
        }

        // Convert the values of the map back to a list
        return new ArrayList<>(map.values());
    }

    @Data
    @Builder
    public static class Municipality {
        private String building;
        private String address;
        private String room;
        private String cityStateZip;
        private String phoneNumber;

        public static Municipality of(TenantDto tenantDto) {
            return Municipality.builder()
                    .building(tenantDto.getAdminOffice())
                    .address(tenantDto.getAdminStreet())
                    .room(tenantDto.getAdminOfficeRoom())
                    .cityStateZip(tenantDto.cityStateZip())
                    .phoneNumber(tenantDto.getClerkPhoneNumber())
                    .build();
        }
    }

    public void setTenant(TenantDto tenantDto) {
        this.tenant = tenantDto;
        this.municipality = Municipality.of(tenantDto);
    }
}

