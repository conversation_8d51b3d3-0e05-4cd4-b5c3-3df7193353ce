package com.scube.payment.features.providers.elavon;

import com.scube.config_utils.app_property.AppPropertyValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Profile("!test")
public class ElavonHttpClient {
    @AppPropertyValue
    private ElavonProperties elavonProperties;

    private WebClient getWebClient() {
        return WebClient.builder()
                .baseUrl(elavonProperties.getUrl())
                .build();
    }

    public String getTransactionToken(String orderId, BigDecimal amount) {
        var webClient = getWebClient();
        var body = new LinkedMultiValueMap<String, String>();
        body.add("ssl_transaction_type", elavonProperties.getTransactionType());
        body.add("ssl_account_id", elavonProperties.getAccountId());
        body.add("ssl_user_id", elavonProperties.getUserId());
        body.add("ssl_pin", elavonProperties.getPin());
        body.add("ssl_amount", amount.toString());
        body.add("ssl_transaction_currency", elavonProperties.getTransactionCurrency());
        body.add("orderId", orderId);
        return webClient.post()
                .uri("/hosted-payments/transaction_token")
                .header("Content-Type", "application/x-www-form-urlencoded")
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }

    public Map<String, String> refundTransaction(String originalTxnId, BigDecimal refundAmount, String billingPhone) {
        var webClient = getWebClient();
        var body = new LinkedMultiValueMap<String, String>();
        body.add("ssl_transaction_type", "ccreturn"); // Refund
        body.add("ssl_merchant_id", elavonProperties.getAccountId());
        body.add("ssl_user_id", elavonProperties.getUserId());
        body.add("ssl_pin", elavonProperties.getPin());
        body.add("ssl_txn_id", originalTxnId);
        body.add("ssl_amount", refundAmount.toPlainString());
        body.add("ssl_transaction_currency", elavonProperties.getTransactionCurrency());
        body.add("ssl_phone", billingPhone);
        body.add("ssl_show_form", "false");
        body.add("ssl_result_format", "ASCII");

        var response = webClient.post()
                .uri(elavonProperties.getVirtualMerchantProcessUrl())
                .header("Content-Type", "application/x-www-form-urlencoded")
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .block();

        Map<String, String> resultMap = parseResponseToMap(response);

        return resultMap;
    }

    /**
     * Parses a response string into a map by splitting on new lines and '=' characters.
     * Only lines with a valid key-value pair are included.
     */
    private Map<String, String> parseResponseToMap(String response) {
        return Arrays.stream(response.split("\n"))
                .map(line -> line.split("=", 2))
                .filter(pair -> pair.length == 2)
                .collect(Collectors.toMap(
                        pair -> pair[0].trim(),
                        pair -> pair[1].trim()
                ));
    }

}