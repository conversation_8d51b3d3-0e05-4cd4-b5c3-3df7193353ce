package com.scube.payment.features.providers.elavon;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.payment.features.payment.enums.RefundStatusCodeElavon;
import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.service.WebhookInboxStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

import static com.scube.payment.features.providers.elavon.ElavonProperties.PAYMENT_PROVIDER_NAME;

@Slf4j
@Service(PAYMENT_PROVIDER_NAME)
@RequiredArgsConstructor
@Profile("!test")
public class ElavonWebhookEventService implements IPaymentProviderWebhookService<ElavonEvent> {
    private final ElavonGateway elavonGateway;
    private final WebhookInboxStorageService webhookInboxStorageService;
    private final ObjectMapper objectMapper;

    public void processWebhook(ElavonEvent event) {
        switch (event.getTransactionType()) {
            case "RETURN" -> {
                if (RefundStatusCodeElavon.APPROVED.toString().equals(event.getResult())) {
                    elavonGateway.refund(event);
                } else {
                    elavonGateway.refundFailed(event);
                }
            }
            case "SALE" -> {
                log.debug("Processing authCapture webhook: {}", event);
                elavonGateway.authCapture(event);
            }
            default ->
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown transaction type: " + event.getTransactionType());
        }
    }

    @Override
    public void store(ElavonEvent elavonEvent) {
        webhookInboxStorageService.store(PAYMENT_PROVIDER_NAME, elavonEvent, elavonEvent.getId());
    }

    public void store(Map<String, String> payload) {
        ElavonEvent elavonEvent = objectMapper.convertValue(payload, ElavonEvent.class);
        store(elavonEvent);
    }

    @Override
    public void process(WebhookInbox inbox) {
        Map<String, Object> payload = inbox.getPayload();
        ElavonEvent event = objectMapper.convertValue(payload, ElavonEvent.class);
        processWebhook(event);
    }

    @Override
    public PaymentDetailsEvent getPaymentDetails(WebhookInbox inbox) {
        Map<String, Object> payload = inbox.getPayload();
        ElavonEvent event = objectMapper.convertValue(payload, ElavonEvent.class);
        return PaymentDetailsEvent.builder()
                .orderId(UUID.fromString(event.getOrderId()))
                .build();
    }
}