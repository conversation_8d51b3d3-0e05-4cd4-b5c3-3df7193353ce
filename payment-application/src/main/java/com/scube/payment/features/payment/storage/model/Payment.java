package com.scube.payment.features.payment.storage.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.payment.features.payment.enums.PaymentStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Getter
@Setter
@Table(name = "payment", indexes = {
        @Index(name = "idx_webhook_payment_payment_reference", columnList = "paymentReference")
})
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class Payment extends AuditableEntity {
    @Column(updatable = false)
    private UUID orderId;

    @PositiveOrZero
    private BigDecimal amount;

    @Size(max = 255)
    private String transactionId;

    @Size(max = 255)
    private String paymentNumber;

    @Enumerated(EnumType.STRING)
    private PaymentStatus status;

    @Size(max = 255)
    private String paymentProvider;

    @Size(max = 255)
    private String paymentType;

    private Instant transactionDate;
    private UUID receiptId;

    @Size(max = 255)
    private String paymentReference;

    private Instant authorizedTs;
    private Instant capturedTs;
    private Instant settledTs;
    private Instant refundedTs;
    private Instant voidedTs;
    private Boolean isOnlineTransaction;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "payee_id")
    private Payee payee;

    public void _void() {
        this.setStatus(PaymentStatus.VOIDED);
        this.setVoidedTs(Instant.now());
    }

    public void refund() {
        this.setStatus(PaymentStatus.REFUNDED);
        this.setRefundedTs(Instant.now());
    }

    public void settle() {
        this.setSettledTs(Instant.now());
    }
}