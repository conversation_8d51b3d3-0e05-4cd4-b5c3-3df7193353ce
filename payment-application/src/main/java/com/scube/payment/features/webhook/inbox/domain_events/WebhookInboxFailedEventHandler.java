package com.scube.payment.features.webhook.inbox.domain_events;

import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class WebhookInboxFailedEventHandler {
    private final AmqpGateway gateway;

    @EventListener
    public void handle(WebhookInboxFailedEvent event) {
        gateway.publish(new WebhookInboxFailedEventRMQ(
                event.getWebhookInboxUuid(),
                event.getDetails()
        ));
    }

    public record WebhookInboxFailedEventRMQ(UUID webhookInboxUuid, PaymentDetailsEvent details) implements IRabbitFanoutPublisher {
    }
}