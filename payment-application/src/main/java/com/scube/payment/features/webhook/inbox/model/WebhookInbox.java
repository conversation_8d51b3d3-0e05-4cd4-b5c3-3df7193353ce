package com.scube.payment.features.webhook.inbox.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.payment.features.payment.storage.model.converter.HashMapConverter;
import com.scube.payment.features.webhook.inbox.domain_events.PaymentDetailsEvent;
import com.scube.payment.features.webhook.inbox.domain_events.WebhookInboxCompletedEvent;
import com.scube.payment.features.webhook.inbox.domain_events.WebhookInboxFailedEvent;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.envers.Audited;

import java.util.Map;

@Getter
@Setter
@Table(name = "webhook_inbox", indexes = {
        @Index(name = "idx_webhook_inbox_provider_webhook_id", columnList = "providerWebhookId")
})
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class WebhookInbox extends AuditableEntity {
    @Enumerated(EnumType.STRING)
    private InboxStatus status;

    @Column(name = "payment_provider")
    @Size(max = 255)
    private String paymentProvider;

    @Convert(converter = HashMapConverter.class)
    @Column(columnDefinition = "jsonb")
    @ColumnTransformer(write = "?::jsonb")
    private Map<String, Object> payload;

    @Size(max = 255)
    private String providerWebhookId;

    public WebhookInbox(String provider, Map<String, Object> payload, String providerWebhookId) {
        this.providerWebhookId = providerWebhookId;
        this.status = InboxStatus.PENDING;
        this.payload = payload;
        this.paymentProvider = provider;
    }

    public void markCompleted(PaymentDetailsEvent details) {
        this.status = InboxStatus.COMPLETED;
        this.registerEvent(new WebhookInboxCompletedEvent(this.getUuid(), details));
    }

    public void markFailed(PaymentDetailsEvent details) {
        this.status = InboxStatus.FAILED;
        this.registerEvent(new WebhookInboxFailedEvent(this.getUuid(), details));
    }

    public boolean isPending() {
        return this.status == InboxStatus.PENDING;
    }

    public boolean isCompleted() {
        return this.status == InboxStatus.COMPLETED;
    }

    public boolean isFailed() {
        return this.status == InboxStatus.FAILED;
    }
}
