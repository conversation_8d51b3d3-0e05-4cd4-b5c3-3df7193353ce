package com.scube.payment.features.payment.receipts.service;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.auth.library.ITokenService;
import com.scube.auth.library.enabled_true.keycloak.services.KeycloakUserService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItemFee;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.tenant.TenantDto;
import com.scube.payment.features.payment.receipts.dto.ReceiptTemplateData;
import com.scube.payment.features.payment.receipts.rabbit.GenerateDocumentCommand;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.AbstractUserRepresentation;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.scube.payment.features.payment.receipts.dto.ReceiptTemplateData.combineDuplicateItems;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReceiptService {
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;
    private final ITokenService tokenService;
    private final KeycloakUserService userService;

    @JsonStorageValue({"config=tenant"})
    private TenantDto tenant;

    public void generateReceipt(SubmitPaymentRequestDto payment, UUID paymentId, OrderInvoiceResponse cartInvoice) {
        log.debug("Generating receipt for payment {}", payment);
        //Call DocumentTemplate Service to generate template
        ReceiptTemplateData receiptTemplateData = new ReceiptTemplateData();

        List<ReceiptTemplateData.ReceiptItem> receiptItems = new ArrayList<>();

        /**
         * Dog Licenses can not be combined in the receipt, so they have to be stored in a separate list
         * that won't be combined at the end.
         */
        List<ReceiptTemplateData.ReceiptItem> dogLicenseReceiptItems = new ArrayList<>();

        /**
         * Loop through invoice items and create receipt items
         */
        var userId = getUserName(payment, cartInvoice);
        for (var item : cartInvoice.getItems()) {
            ReceiptTemplateData.ReceiptItem receiptItem = new ReceiptTemplateData.ReceiptItem();
            if (item == null) continue;

            receiptItem.setItem(cleanString(formatItem(item)));
            receiptItem.setUnitPrice(item.getTotal().setScale(2, RoundingMode.CEILING).toString());
            receiptItem.setNote(cleanString(item.getSecondaryDisplay()));
            receiptItem.setUserId(userId);
            receiptItem.setQuantity("1");
            receiptItem.setAmount(item.getTotal().setScale(2, RoundingMode.CEILING).toString());

            /**
             * Separate dog licenses from other items
             */
            if (item.getItemType().equals("license")) {
                dogLicenseReceiptItems.add(receiptItem);
            } else {
                receiptItems.add(receiptItem);
            }
        }

        /**
         * Combine duplicate items into a single item with an updated quantity and amount
         */
        receiptItems = combineDuplicateItems(receiptItems);

        receiptItems.addAll(dogLicenseReceiptItems);

        var subTotal = cartInvoice.getSubtotal().setScale(2, RoundingMode.CEILING);
        var discounts = cartInvoice.getItems().stream().map(OrderInvoiceItem::getDiscount)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.CEILING);
        var total = cartInvoice.getTotal().setScale(2, RoundingMode.CEILING);

        var paymentAmount = payment.getPaymentAmount();
        var balance = total.subtract(paymentAmount);

        var payee = ReceiptTemplateData.Payee.of(payment.getPayee());

        receiptTemplateData.setOrderNumber(" " + cartInvoice.getOrderNumber());
        receiptTemplateData.setOrderDate(parseInstant(payment.getTransactionDate(), USA_DATE_FORMAT));
        receiptTemplateData.setOrderTime(parseInstant(payment.getTransactionDate(), USA_TIME_FORMAT));
        receiptTemplateData.setReceiptItems(receiptItems);
        receiptTemplateData.setSubTotal(subTotal.toString());
        receiptTemplateData.setDiscounts(discounts.toString());
        receiptTemplateData.setTotal(paymentAmount.setScale(2, RoundingMode.CEILING).toString());

        if (payment.getPaymentType().equalsIgnoreCase("check")) {
            String paymentReference = (isBlank(payment.getPaymentReference()) ? "" : "\n#" + payment.getPaymentReference());
            receiptTemplateData.setPaymentType(camelCaseToCapitalizedWords(payment.getPaymentType()) + paymentReference);
        } else {
            receiptTemplateData.setPaymentType(camelCaseToCapitalizedWords(payment.getPaymentType()));
        }

        receiptTemplateData.setBalance(balance.toString());
        receiptTemplateData.setPayee(List.of(payee));
        receiptTemplateData.setNotes("");
        receiptTemplateData.setTenant(tenant);

        log.debug("Receipt data: {}", getString(receiptTemplateData));

        amqpGateway.publish(new GenerateDocumentCommand(paymentId.toString(),
                "paymentReceipt", cartInvoice.getOrderId().toString(), "ReceiptTemplate", getString(receiptTemplateData)));
    }

    private String getUserName(SubmitPaymentRequestDto payment, OrderInvoiceResponse orderInvoice) {
        var loggedInUsername = tokenService.getLoggedInUserInfo().getPreferredUsername();
        if (!loggedInUsername.contains("service-account")) {
            return loggedInUsername;
        }
        return Optional.ofNullable(orderInvoice.getUserId())
                .map(userService::findById)
                .map(AbstractUserRepresentation::getUsername)
                .orElse(payment.getPayee().getEmail());

    }

    public static String formatItem(OrderInvoiceItem item) {
        if (item == null) {
            return "";
        } else if (item.getItemType().equals("license") && item.getPrimaryDisplay().equals("Dog License")) {
            return formatDogLicense(item);
        } else {
            return cleanString(item.getPrimaryDisplay());
        }
    }

    public static String formatDogLicense(OrderInvoiceItem item) {
        List<OrderInvoiceItemFee> fees = item.getFees();
        if (fees == null || fees.isEmpty()) {
            return "Dog License";
        } else if (fees.stream().anyMatch(f -> f.getFeeCode().equals("DL-M-UNALT"))) {
            return "Dog License, Unaltered";
        } else {
            return "Dog License, Altered";
        }
    }

    private static String cleanString(String str) {
        return str == null ? "" : str.trim();
    }

    public JsonNode getString(Object data) {
        log.debug("ReportService.getString()");
        return objectMapper.valueToTree(data);
    }

    public static String USA_DATE_FORMAT = "MM/dd/yyyy";
    public static String USA_TIME_FORMAT = "hh:mm a";
    public static String USA_DATE_TIME_FORMAT = "MM/dd/yyyy hh:mm a";

    public static String parseInstant(Instant instant, String pattern) {
        if (instant == null) {
            instant = Instant.now();
        }
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("America/New_York"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return zonedDateTime.toLocalDateTime().format(formatter);
    }

    public static String camelCaseToCapitalizedWords(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder(input.length());
        int inputLength = input.length();

        result.append(Character.toUpperCase(input.charAt(0)));

        for (int i = 1; i < inputLength; i++) {
            char currentChar = input.charAt(i);

            if (Character.isUpperCase(currentChar)) {
                result.append(' ').append(currentChar);
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }
}

