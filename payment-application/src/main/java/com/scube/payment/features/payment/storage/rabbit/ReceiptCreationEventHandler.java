package com.scube.payment.features.payment.storage.rabbit;

import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@Slf4j
@AllArgsConstructor
public class ReceiptCreationEventHandler extends FanoutListener<ReceiptCreationEventHandler.DocumentGeneratedEvent> {
    private final PaymentStorageService paymentStorageService;

    @Transactional
    @Override
    public void consume(DocumentGeneratedEvent event) {
        log.info("DocumentGeneratedEvent received: {}", event);
        var topic = event.getParentType();
        try {
            if (topic != null && topic.equals("paymentReceipt")) {
                paymentStorageService.storeReceiptId(UUID.fromString(event.getParentId()), UUID.fromString(event.getDocumentId()));
                log.info("DocumentGeneratedEvent processed: {}", event);
            }
        } catch (Exception e) {
            log.error("Error while processing DocumentGeneratedEvent", e);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentGeneratedEvent implements IRabbitFanoutSubscriber {
        private String parentId;
        private String parentType;
        private String documentId;
    }
}

