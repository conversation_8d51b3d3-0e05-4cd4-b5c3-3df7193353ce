package com.scube.record.features.association_type.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.association_type.dto.AssociationTypeResponse;
import com.scube.record.features.association_type.dto.CreateAssociationTypeRequest;
import com.scube.record.features.association_type.dto.UpdateAssociationTypeRequest;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class AssociationTypeMapper {

    private final ObjectMapper objectMapper;

    public AssociationType toEntity(CreateAssociationTypeRequest request) {
        AssociationType associationType = new AssociationType();
        associationType.setAssociationName(request.getAssociationName());

        // Build properties JSON
        ObjectNode properties = objectMapper.createObjectNode();
        properties.put("type_code", request.getTypeCode());
        properties.put("description", request.getDescription());

        // Add parent record types array
        ArrayNode parentTypes = objectMapper.createArrayNode();
        request.getParentRecordTypes().forEach(parentTypes::add);
        properties.set("parent_record_types", parentTypes);

        // Add child record types array
        ArrayNode childTypes = objectMapper.createArrayNode();
        request.getChildRecordTypes().forEach(childTypes::add);
        properties.set("child_record_types", childTypes);

        // Merge additional properties if provided
        if (request.getAdditionalProperties() != null && request.getAdditionalProperties().isObject()) {
            request.getAdditionalProperties().fields().forEachRemaining(entry -> {
                properties.set(entry.getKey(), entry.getValue());
            });
        }

        associationType.setProperties(properties);
        associationType.setCreatedBy(request.getCreatedBy());
        associationType.setLastModifiedBy(request.getCreatedBy());

        return associationType;
    }

    public AssociationTypeResponse toResponse(AssociationType associationType) {
        List<String> parentRecordTypes = new ArrayList<>();
        List<String> childRecordTypes = new ArrayList<>();
        String typeCode = null;
        String description = null;

        if (associationType.getProperties() != null) {
            // Extract type_code
            if (associationType.getProperties().has("type_code")) {
                typeCode = associationType.getProperties().get("type_code").asText();
            }

            // Extract description
            if (associationType.getProperties().has("description")) {
                description = associationType.getProperties().get("description").asText();
            }

            // Extract parent_record_types
            if (associationType.getProperties().has("parent_record_types")) {
                associationType.getProperties().get("parent_record_types").forEach(node -> {
                    parentRecordTypes.add(node.asText());
                });
            }

            // Extract child_record_types
            if (associationType.getProperties().has("child_record_types")) {
                associationType.getProperties().get("child_record_types").forEach(node -> {
                    childRecordTypes.add(node.asText());
                });
            }
        }

        return AssociationTypeResponse.builder()
                .associationTypeId(associationType.getAssociationTypeId())
                .associationTypeUuid(associationType.getAssociationTypeUuid())
                .associationName(associationType.getAssociationName())
                .typeCode(typeCode)
                .description(description)
                .parentRecordTypes(parentRecordTypes)
                .childRecordTypes(childRecordTypes)
                .properties(associationType.getProperties())
                .createdAt(associationType.getCreatedAt())
                .createdBy(associationType.getCreatedBy())
                .lastModifiedAt(associationType.getLastModifiedAt())
                .lastModifiedBy(associationType.getLastModifiedBy())
                .build();
    }

    public void updateEntity(AssociationType associationType, UpdateAssociationTypeRequest request) {
        if (request.getAssociationName() != null) {
            associationType.setAssociationName(request.getAssociationName());
        }

        ObjectNode properties = (ObjectNode) associationType.getProperties();
        if (properties == null) {
            properties = objectMapper.createObjectNode();
        }

        // Make it final for lambda usage
        final ObjectNode finalProperties = properties;

        if (request.getDescription() != null) {
            finalProperties.put("description", request.getDescription());
        }

        if (request.getParentRecordTypes() != null) {
            ArrayNode parentTypes = objectMapper.createArrayNode();
            request.getParentRecordTypes().forEach(parentTypes::add);
            finalProperties.set("parent_record_types", parentTypes);
        }

        if (request.getChildRecordTypes() != null) {
            ArrayNode childTypes = objectMapper.createArrayNode();
            request.getChildRecordTypes().forEach(childTypes::add);
            finalProperties.set("child_record_types", childTypes);
        }

        if (request.getAdditionalProperties() != null && request.getAdditionalProperties().isObject()) {
            request.getAdditionalProperties().fields().forEachRemaining(entry -> {
                finalProperties.set(entry.getKey(), entry.getValue());
            });
        }

        associationType.setProperties(finalProperties);
        associationType.setLastModifiedBy(request.getLastModifiedBy());
    }
}