package com.scube.record.features.association_type.service;

import com.scube.record.features.association_type.dto.AssociationTypeResponse;
import com.scube.record.features.association_type.dto.CreateAssociationTypeRequest;
import com.scube.record.features.association_type.dto.UpdateAssociationTypeRequest;
import com.scube.record.features.association_type.exception.AssociationTypeNotFoundException;
import com.scube.record.features.association_type.exception.DuplicateAssociationTypeException;
import com.scube.record.features.association_type.mapper.AssociationTypeMapper;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import com.scube.record.infrastructure.db.repository.record.AssociationTypeRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class AssociationTypeService {

    private final AssociationTypeRepository associationTypeRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final AssociationTypeMapper associationTypeMapper;

    public AssociationTypeResponse createAssociationType(CreateAssociationTypeRequest request) {
        // Check for duplicate by name
        if (associationTypeRepository.existsByAssociationName(request.getAssociationName())) {
            throw new DuplicateAssociationTypeException(
                "Association type already exists with name: " + request.getAssociationName());
        }

        // Check for duplicate by type code
        Optional<AssociationType> existingByCode = associationTypeRepository.findByTypeCode(request.getTypeCode());
        if (existingByCode.isPresent()) {
            throw new DuplicateAssociationTypeException(
                "Association type already exists with type code: " + request.getTypeCode());
        }

        // Validate parent and child record types exist
        validateRecordTypes(request.getParentRecordTypes(), "parent");
        validateRecordTypes(request.getChildRecordTypes(), "child");

        AssociationType associationType = associationTypeMapper.toEntity(request);
        AssociationType saved = associationTypeRepository.save(associationType);
        return associationTypeMapper.toResponse(saved);
    }

    public AssociationTypeResponse updateAssociationType(String associationTypeUuid, UpdateAssociationTypeRequest request) {
        AssociationType associationType = associationTypeRepository.findByAssociationTypeUuid(associationTypeUuid)
            .orElseThrow(() -> new AssociationTypeNotFoundException(
                "Association type not found with UUID: " + associationTypeUuid));

        // Validate record types if provided
        if (request.getParentRecordTypes() != null) {
            validateRecordTypes(request.getParentRecordTypes(), "parent");
        }
        if (request.getChildRecordTypes() != null) {
            validateRecordTypes(request.getChildRecordTypes(), "child");
        }

        associationTypeMapper.updateEntity(associationType, request);
        AssociationType updated = associationTypeRepository.save(associationType);
        return associationTypeMapper.toResponse(updated);
    }

    @Transactional(readOnly = true)
    public AssociationTypeResponse getAssociationTypeByUuid(String associationTypeUuid) {
        AssociationType associationType = associationTypeRepository.findByAssociationTypeUuid(associationTypeUuid)
            .orElseThrow(() -> new AssociationTypeNotFoundException(
                "Association type not found with UUID: " + associationTypeUuid));
        return associationTypeMapper.toResponse(associationType);
    }

    @Transactional(readOnly = true)
    public AssociationTypeResponse getAssociationTypeByCode(String typeCode) {
        AssociationType associationType = associationTypeRepository.findByTypeCode(typeCode)
            .orElseThrow(() -> new AssociationTypeNotFoundException(
                "Association type not found with type code: " + typeCode));
        return associationTypeMapper.toResponse(associationType);
    }

    @Transactional(readOnly = true)
    public Optional<AssociationTypeResponse> findAssociationTypeByUuid(String associationTypeUuid) {
        return associationTypeRepository.findByAssociationTypeUuid(associationTypeUuid)
            .map(associationTypeMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<AssociationTypeResponse> getAllAssociationTypes(Pageable pageable) {
        Page<AssociationType> associationTypes = associationTypeRepository.findAll(pageable);
        List<AssociationTypeResponse> responses = associationTypes.getContent().stream()
            .map(associationTypeMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, associationTypes.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<AssociationTypeResponse> getAllAssociationTypes() {
        return associationTypeRepository.findAll().stream()
            .map(associationTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<AssociationTypeResponse> getAssociationTypesByParentRecordType(String recordType) {
        return associationTypeRepository.findByParentRecordType(recordType).stream()
            .map(associationTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<AssociationTypeResponse> getAssociationTypesByChildRecordType(String recordType) {
        return associationTypeRepository.findByChildRecordType(recordType).stream()
            .map(associationTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Page<AssociationTypeResponse> searchAssociationTypes(String keyword, Pageable pageable) {
        Page<AssociationType> associationTypes = associationTypeRepository.searchByKeyword(keyword, pageable);
        List<AssociationTypeResponse> responses = associationTypes.getContent().stream()
            .map(associationTypeMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, associationTypes.getTotalElements());
    }

    public void deleteAssociationType(String associationTypeUuid) {
        AssociationType associationType = associationTypeRepository.findByAssociationTypeUuid(associationTypeUuid)
            .orElseThrow(() -> new AssociationTypeNotFoundException(
                "Association type not found with UUID: " + associationTypeUuid));
        associationTypeRepository.delete(associationType);
    }

    @Transactional(readOnly = true)
    public boolean existsByUuid(String associationTypeUuid) {
        return associationTypeRepository.existsByAssociationTypeUuid(associationTypeUuid);
    }

    @Transactional(readOnly = true)
    public long countAssociationTypes() {
        return associationTypeRepository.count();
    }

    /**
     * Validates that all record types exist in the database
     */
    private void validateRecordTypes(List<String> recordTypes, String typeLabel) {
        for (String typeCode : recordTypes) {
            if (!recordTypeRepository.existsByTypeCode(typeCode)) {
                throw new IllegalArgumentException(
                    "Invalid " + typeLabel + " record type: " + typeCode + " does not exist");
            }
        }
    }
}