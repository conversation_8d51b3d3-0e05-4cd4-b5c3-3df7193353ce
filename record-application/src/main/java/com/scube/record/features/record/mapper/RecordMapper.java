package com.scube.record.features.record.mapper;

import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import org.springframework.stereotype.Component;

@Component
public class RecordMapper {

    public Record toEntity(CreateRecordRequest request) {
        Record record = new Record();
        record.setRecordName(request.getRecordName());
        record.setStatus(request.getStatus());
        record.setProperties(request.getProperties());
        record.setCreatedBy(request.getCreatedBy());
        record.setLastModifiedBy(request.getCreatedBy());
        return record;
    }

    public RecordResponse toResponse(Record record) {
        return RecordResponse.builder()
                .recordId(record.getRecordId())
                .recordUuid(record.getRecordUuid())
                .recordName(record.getRecordName())
                .status(record.getStatus())
                .properties(record.getProperties())
                .recordTypeCode(record.getRecordType() != null ? record.getRecordType().getTypeCode() : null)
                .recordTypeName(record.getRecordType() != null ? record.getRecordType().getTypeName() : null)
                .createdAt(record.getCreatedAt())
                .createdBy(record.getCreatedBy())
                .lastModifiedAt(record.getLastModifiedAt())
                .lastModifiedBy(record.getLastModifiedBy())
                .build();
    }

    public void updateEntity(Record record, UpdateRecordRequest request) {
        if (request.getRecordName() != null) {
            record.setRecordName(request.getRecordName());
        }
        if (request.getStatus() != null) {
            record.setStatus(request.getStatus());
        }
        if (request.getProperties() != null) {
            record.setProperties(request.getProperties());
        }
        record.setLastModifiedBy(request.getLastModifiedBy());
    }
}