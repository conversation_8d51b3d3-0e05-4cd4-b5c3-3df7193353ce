package com.scube.record.features.profile.service;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class RejectedFieldsService {

    private final ConcurrentHashMap<String, Set<String>> rejectedFieldsStore = new ConcurrentHashMap<>();

    private String key(UUID recordUuid) {
        return recordUuid.toString();
    }

    private String key(String recordType, Long recordId) {
        return recordType + ":" + recordId;
    }

    // ========== UUID-based methods (NEW) ==========

    public void addToRejectedFieldListByUuid(UUID recordUuid, Set<String> fields, boolean merge) {
        if (fields == null || fields.isEmpty()) return;
        rejectedFieldsStore.merge(key(recordUuid), ConcurrentHashMap.newKeySet(fields.size()), (oldSet, newSet) -> oldSet);
        rejectedFieldsStore.get(key(recordUuid)).addAll(fields);
    }

    public void removeFromRejectedFieldListByUuid(UUID recordUuid, Set<String> fields, boolean ignoreMissing) {
        if (fields == null || fields.isEmpty()) return;
        var set = rejectedFieldsStore.get(key(recordUuid));
        if (set != null) {
            set.removeAll(fields);
            if (set.isEmpty()) {
                rejectedFieldsStore.remove(key(recordUuid));
            }
        }
    }

    public Set<String> getRejectedFieldListByUuid(UUID recordUuid) {
        return rejectedFieldsStore.getOrDefault(key(recordUuid), Collections.emptySet());
    }

    // ========== Legacy methods (keep for compatibility) ==========

    public void addToRejectedFieldList(String recordType, Long recordId, Set<String> fields, boolean merge) {
        if (fields == null || fields.isEmpty()) return;
        rejectedFieldsStore.merge(key(recordType, recordId), ConcurrentHashMap.newKeySet(fields.size()), (oldSet, newSet) -> oldSet);
        rejectedFieldsStore.get(key(recordType, recordId)).addAll(fields);
    }

    public void removeFromRejectedFieldList(String recordType, Long recordId, Set<String> fields, boolean ignoreMissing) {
        if (fields == null || fields.isEmpty()) return;
        var set = rejectedFieldsStore.get(key(recordType, recordId));
        if (set != null) {
            set.removeAll(fields);
            if (set.isEmpty()) {
                rejectedFieldsStore.remove(key(recordType, recordId));
            }
        }
    }

    public void clearRejectedFieldList(String recordType, Long recordId) {
        rejectedFieldsStore.remove(key(recordType, recordId));
    }

    public Set<String> getRejectedFieldList(String recordType, Long recordId) {
        return rejectedFieldsStore.getOrDefault(key(recordType, recordId), Collections.emptySet());
    }
}