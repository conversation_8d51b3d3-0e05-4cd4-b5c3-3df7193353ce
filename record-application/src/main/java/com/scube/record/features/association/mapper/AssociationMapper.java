package com.scube.record.features.association.mapper;

import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.features.association.dto.AssociationResponse;
import com.scube.record.features.association.dto.CreateAssociationRequest;
import com.scube.record.features.association.dto.UpdateAssociationRequest;
import org.springframework.stereotype.Component;

@Component
public class AssociationMapper {

    public Association toEntity(CreateAssociationRequest request) {
        Association association = new Association();
        association.setParentType(request.getParentType());
        association.setChildType(request.getChildType());
        association.setParentId(request.getParentId());
        association.setChildId(request.getChildId());
        association.setAssociationTypeId(request.getAssociationTypeId());
        association.setProperties(request.getProperties());
        association.setCreatedBy(request.getCreatedBy());
        association.setLastModifiedBy(request.getCreatedBy());
        return association;
    }

    public AssociationResponse toResponse(Association association) {
        return AssociationResponse.builder()
                .associationId(association.getAssociationId())
                .associationUuid(association.getAssociationUuid())
                .parentType(association.getParentType())
                .childType(association.getChildType())
                .parentId(association.getParentId())
                .childId(association.getChildId())
                .associationTypeId(association.getAssociationTypeId())
                .properties(association.getProperties())
                .createdAt(association.getCreatedAt())
                .createdBy(association.getCreatedBy())
                .lastModifiedAt(association.getLastModifiedAt())
                .lastModifiedBy(association.getLastModifiedBy())
                .build();
    }

    public void updateEntity(Association association, UpdateAssociationRequest request) {
        if (request.getAssociationTypeId() != null) {
            association.setAssociationTypeId(request.getAssociationTypeId());
        }
        if (request.getProperties() != null) {
            association.setProperties(request.getProperties());
        }
        association.setLastModifiedBy(request.getLastModifiedBy());
    }
}