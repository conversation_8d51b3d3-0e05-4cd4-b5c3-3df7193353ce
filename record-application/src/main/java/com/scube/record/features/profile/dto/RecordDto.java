package com.scube.record.features.profile.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordDto {
    
    private Long recordId;
    private UUID recordUuid;
    private String recordName;
    private String recordType;
    private String status;
    private JsonNode properties;
    private String recordTypeCode;
    private String recordTypeName;
    private JsonNode recordTypeProperties;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    
    // Additional metadata for generic record handling
    private String entityType;
    private Long entityId;
    private String description;
    private JsonNode metadata;

    // Rejected fields for workflow management
    private Set<String> rejectedFields;
}
