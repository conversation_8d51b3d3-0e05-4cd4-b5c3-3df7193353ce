package com.scube.record.features.record.service;

import com.scube.record.features.association_type.exception.AssociationTypeNotFoundException;
import com.scube.record.features.record.dto.RecordAssociationRequest;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.repository.record.AssociationRepository;
import com.scube.record.infrastructure.db.repository.record.AssociationTypeRepository;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecordAssociationService {

    private final AssociationRepository associationRepository;
    private final AssociationTypeRepository associationTypeRepository;
    private final RecordRepository recordRepository;

    /**
     * Creates bidirectional associations for a record based on the associations list.
     * Validates association types and record types before creating associations.
     *
     * @param record The record to create associations for
     * @param associationRequests List of association requests
     * @param createdBy User creating the associations
     */
    @Transactional
    public void createAssociations(Record record, List<RecordAssociationRequest> associationRequests, String createdBy) {
        if (associationRequests == null || associationRequests.isEmpty()) {
            return;
        }

        for (RecordAssociationRequest request : associationRequests) {
            createBidirectionalAssociation(record, request, createdBy);
        }
    }

    /**
     * Creates a bidirectional association between two records.
     * Automatically determines parent/child relationship based on association type configuration.
     */
    @Transactional
    public void createBidirectionalAssociation(Record record, RecordAssociationRequest request, String createdBy) {
        // 1. Fetch the association type
        AssociationType associationType = associationTypeRepository.findByTypeCode(request.getAssociationTypeCode())
                .orElseThrow(() -> new AssociationTypeNotFoundException(
                        "Association type not found with code: " + request.getAssociationTypeCode()));

        // 2. Fetch the associated record
        Record associatedRecord = recordRepository.findByRecordUuid(UUID.fromString(request.getAssociatedRecordUuid()))
                .orElseThrow(() -> new RecordNotFoundException(
                        "Associated record not found with UUID: " + request.getAssociatedRecordUuid()));

        // 3. Determine parent and child based on association type configuration
        Record parentRecord;
        Record childRecord;

        if (request.getRole() != null && !request.getRole().isEmpty()) {
            // Use explicit role if provided
            if (request.getRole().equalsIgnoreCase("PARENT")) {
                parentRecord = record;
                childRecord = associatedRecord;
            } else if (request.getRole().equalsIgnoreCase("CHILD")) {
                parentRecord = associatedRecord;
                childRecord = record;
            } else {
                throw new IllegalArgumentException("Invalid role: " + request.getRole() + ". Must be PARENT or CHILD");
            }
        } else {
            // Auto-determine based on association type configuration
            boolean recordCanBeParent = canBeParent(associationType, record);
            boolean recordCanBeChild = canBeChild(associationType, record);
            boolean associatedCanBeParent = canBeParent(associationType, associatedRecord);
            boolean associatedCanBeChild = canBeChild(associationType, associatedRecord);

            if (recordCanBeParent && associatedCanBeChild) {
                parentRecord = record;
                childRecord = associatedRecord;
            } else if (recordCanBeChild && associatedCanBeParent) {
                parentRecord = associatedRecord;
                childRecord = record;
            } else {
                throw new IllegalArgumentException(
                        String.format("Association type '%s' does not allow relationship between record types '%s' and '%s'",
                                associationType.getAssociationName(),
                                record.getRecordType().getTypeCode(),
                                associatedRecord.getRecordType().getTypeCode()));
            }
        }

        // 4. Validate the relationship is allowed
        validateAssociation(associationType, parentRecord, childRecord);

        // 5. Check if association already exists
        if (associationRepository.existsByParentIdAndChildIdAndAssociationTypeId(
                parentRecord.getRecordId(), childRecord.getRecordId(), associationType.getAssociationTypeId().toString())) {
            log.warn("Association already exists between parent {} and child {} with type {}",
                    parentRecord.getRecordUuid(), childRecord.getRecordUuid(), associationType.getAssociationName());
            return;
        }

        // 6. Create bidirectional associations
        createAssociationPair(parentRecord, childRecord, associationType, createdBy);

        log.info("Created bidirectional association: {} [{}] <-[{}]-> {} [{}]",
                parentRecord.getRecordName(), parentRecord.getRecordType().getTypeCode(),
                associationType.getAssociationName(),
                childRecord.getRecordName(), childRecord.getRecordType().getTypeCode());
    }

    /**
     * Creates a pair of associations (parent->child and child->parent) for bidirectional relationship.
     */
    private void createAssociationPair(Record parentRecord, Record childRecord, AssociationType associationType, String createdBy) {
        // Create parent -> child association
        Association parentToChild = new Association();
        parentToChild.setParentType(parentRecord.getRecordType().getTypeCode());
        parentToChild.setChildType(childRecord.getRecordType().getTypeCode());
        parentToChild.setParentId(parentRecord.getRecordId());
        parentToChild.setChildId(childRecord.getRecordId());
        parentToChild.setAssociationTypeId(associationType.getAssociationTypeId().toString());
        parentToChild.setCreatedBy(createdBy);
        parentToChild.setLastModifiedBy(createdBy);
        associationRepository.save(parentToChild);

        // Create child -> parent association (reverse)
        Association childToParent = new Association();
        childToParent.setParentType(childRecord.getRecordType().getTypeCode());
        childToParent.setChildType(parentRecord.getRecordType().getTypeCode());
        childToParent.setParentId(childRecord.getRecordId());
        childToParent.setChildId(parentRecord.getRecordId());
        childToParent.setAssociationTypeId(associationType.getAssociationTypeId().toString());
        childToParent.setCreatedBy(createdBy);
        childToParent.setLastModifiedBy(createdBy);
        associationRepository.save(childToParent);
    }

    /**
     * Validates if the association is allowed based on association type configuration.
     */
    private void validateAssociation(AssociationType associationType, Record parentRecord, Record childRecord) {
        String parentTypeCode = parentRecord.getRecordType().getTypeCode();
        String childTypeCode = childRecord.getRecordType().getTypeCode();

        boolean parentAllowed = canBeParent(associationType, parentRecord);
        boolean childAllowed = canBeChild(associationType, childRecord);

        if (!parentAllowed) {
            throw new IllegalArgumentException(
                    String.format("Record type '%s' is not allowed as parent in association type '%s'",
                            parentTypeCode, associationType.getAssociationName()));
        }

        if (!childAllowed) {
            throw new IllegalArgumentException(
                    String.format("Record type '%s' is not allowed as child in association type '%s'",
                            childTypeCode, associationType.getAssociationName()));
        }
    }

    /**
     * Checks if a record can be a parent in the given association type.
     */
    private boolean canBeParent(AssociationType associationType, Record record) {
        if (associationType.getProperties() == null || !associationType.getProperties().has("parent_record_types")) {
            return false;
        }

        String recordTypeCode = record.getRecordType().getTypeCode();
        var parentTypes = associationType.getProperties().get("parent_record_types");

        if (parentTypes.isArray()) {
            for (var type : parentTypes) {
                if (type.asText().equals(recordTypeCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Checks if a record can be a child in the given association type.
     */
    private boolean canBeChild(AssociationType associationType, Record record) {
        if (associationType.getProperties() == null || !associationType.getProperties().has("child_record_types")) {
            return false;
        }

        String recordTypeCode = record.getRecordType().getTypeCode();
        var childTypes = associationType.getProperties().get("child_record_types");

        if (childTypes.isArray()) {
            for (var type : childTypes) {
                if (type.asText().equals(recordTypeCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Gets all associations for a record (both where it's parent and where it's child).
     */
    @Transactional(readOnly = true)
    public List<Association> getAssociationsForRecord(Long recordId) {
        return associationRepository.findByParentIdOrChildId(recordId, recordId);
    }

    /**
     * Deletes bidirectional association between two records.
     */
    @Transactional
    public void deleteAssociation(Long parentId, Long childId) {
        // Delete both directions
        associationRepository.deleteByParentIdAndChildId(parentId, childId);
        associationRepository.deleteByParentIdAndChildId(childId, parentId);
    }
}
