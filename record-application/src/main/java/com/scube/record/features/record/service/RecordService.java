package com.scube.record.features.record.service;

import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import com.scube.record.features.record.exception.InvalidRecordTypeException;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.features.record.mapper.RecordMapper;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class RecordService {

    private final RecordRepository recordRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final RecordMapper recordMapper;
    private final RecordAssociationService recordAssociationService;

    public RecordResponse createRecord(CreateRecordRequest request) {
        RecordType recordType = recordTypeRepository.findByTypeCode(request.getRecordTypeCode())
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + request.getRecordTypeCode()));

        Record record = recordMapper.toEntity(request);
        record.setRecordType(recordType);

        Record savedRecord = recordRepository.save(record);

        // Create associations if provided
        recordAssociationService.createAssociations(savedRecord, request.getAssociations(), request.getCreatedBy());

        return recordMapper.toResponse(savedRecord);
    }

    public RecordResponse updateRecord(UUID recordUuid, UpdateRecordRequest request) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));

        recordMapper.updateEntity(record, request);
        Record updatedRecord = recordRepository.save(record);
        return recordMapper.toResponse(updatedRecord);
    }

    @Transactional(readOnly = true)
    public RecordResponse getRecordByUuid(UUID recordUuid) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));
        return recordMapper.toResponse(record);
    }

    @Transactional(readOnly = true)
    public Optional<RecordResponse> findRecordByUuid(UUID recordUuid) {
        return recordRepository.findByRecordUuid(recordUuid)
            .map(recordMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> getAllRecords(Pageable pageable) {
        Page<Record> records = recordRepository.findAll(pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> getRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findByRecordType(recordType).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> getRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> getRecordsByTypeAndStatus(String recordTypeCode, String status, Pageable pageable) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        Page<Record> records = recordRepository.findByRecordTypeAndStatus(recordType, status, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> searchRecords(String keyword, Pageable pageable) {
        Page<Record> records = recordRepository.searchByKeyword(keyword, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> searchRecordsByName(String recordName) {
        return recordRepository.findByRecordNameContaining(recordName).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<String> getRecordNameSuggestions(String query, int limit) {
        return recordRepository.findRecordNameSuggestions(query, limit);
    }

    @Transactional(readOnly = true)
    public List<String> getRecordNameSuggestionsByType(String query, String recordTypeCode, int limit) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findRecordNameSuggestionsByType(query, recordType.getRecordTypeId(), limit);
    }

    public void deleteRecord(UUID recordUuid) {
        if (!recordRepository.existsByRecordUuid(recordUuid)) {
            throw new RecordNotFoundException("Record not found with UUID: " + recordUuid);
        }
        recordRepository.deleteByRecordUuid(recordUuid);
    }

    @Transactional(readOnly = true)
    public boolean existsByUuid(UUID recordUuid) {
        return recordRepository.existsByRecordUuid(recordUuid);
    }

    @Transactional(readOnly = true)
    public long countRecords() {
        return recordRepository.count();
    }

    @Transactional(readOnly = true)
    public long countRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));
        return recordRepository.findByRecordType(recordType).size();
    }

    @Transactional(readOnly = true)
    public long countRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).size();
    }
}