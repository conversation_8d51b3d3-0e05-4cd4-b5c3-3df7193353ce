package com.scube.record.features.record.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordAssociationRequest {

    @NotBlank(message = "Association type code is required")
    private String associationTypeCode;

    @NotBlank(message = "Associated record UUID is required")
    private String associatedRecordUuid;

    /**
     * Optional: Specify the role of this record in the association.
     * If not specified, system will determine based on record types and association type config.
     * Values: "PARENT" or "CHILD"
     */
    private String role;
}
