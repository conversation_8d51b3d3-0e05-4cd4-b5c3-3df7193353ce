package com.scube.record.features.record.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateRecordRequest {

    @NotBlank(message = "Record type code is required")
    private String recordTypeCode;

    @NotBlank(message = "Record name is required")
    private String recordName;

    private String status = "ACTIVE";

    @NotNull(message = "Properties are required")
    private JsonNode properties;

    @NotBlank(message = "Created by is required")
    private String createdBy;

    /**
     * List of associations to create with other records.
     * Each association creates a bidirectional relationship.
     */
    private List<RecordAssociationRequest> associations = new ArrayList<>();
}