package com.scube.record.features.profile.dto;

import com.scube.record.features.association.dto.AssociationResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordAndAssociationsDto {
    
    private RecordDto record;
    private List<AssociationResponse> associations;
    private List<RecordDto> relatedRecords;
    
    // Metadata for the association context
    private String recordType;
    private Integer associationCount;
    private Integer relatedRecordCount;
    private Integer maxDepth;
    
    // Additional context information
    private String requestedBy;
    private String accessLevel;
}
