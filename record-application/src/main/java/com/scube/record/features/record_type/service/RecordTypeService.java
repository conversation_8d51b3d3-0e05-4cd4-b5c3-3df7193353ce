package com.scube.record.features.record_type.service;

import com.scube.record.features.record_type.dto.CreateRecordTypeRequest;
import com.scube.record.features.record_type.dto.RecordTypeResponse;
import com.scube.record.features.record_type.dto.UpdateRecordTypeRequest;
import com.scube.record.features.record_type.exception.DuplicateRecordTypeCodeException;
import com.scube.record.features.record_type.exception.RecordTypeNotFoundException;
import com.scube.record.features.record_type.mapper.RecordTypeMapper;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class RecordTypeService {

    private final RecordTypeRepository recordTypeRepository;
    private final RecordTypeMapper recordTypeMapper;

    public RecordTypeResponse createRecordType(CreateRecordTypeRequest request) {
        if (recordTypeRepository.existsByTypeCode(request.getTypeCode())) {
            throw new DuplicateRecordTypeCodeException(
                "Record type already exists with code: " + request.getTypeCode());
        }

        RecordType recordType = recordTypeMapper.toEntity(request);

        if (request.getParentTypeCode() != null) {
            RecordType parent = recordTypeRepository.findByTypeCode(request.getParentTypeCode())
                .orElseThrow(() -> new RecordTypeNotFoundException(
                    "Parent record type not found with type code: " + request.getParentTypeCode()));
            recordType.setParent(parent);
        }

        RecordType savedRecordType = recordTypeRepository.save(recordType);
        return recordTypeMapper.toResponse(savedRecordType);
    }

    public RecordTypeResponse updateRecordType(UUID recordTypeUuid, UpdateRecordTypeRequest request) {
        RecordType recordType = recordTypeRepository.findByRecordTypeUuid(recordTypeUuid)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with UUID: " + recordTypeUuid));

        if (request.getParentTypeCode() != null) {
            RecordType parent = recordTypeRepository.findByTypeCode(request.getParentTypeCode())
                .orElseThrow(() -> new RecordTypeNotFoundException(
                    "Parent record type not found with type code: " + request.getParentTypeCode()));
            recordType.setParent(parent);
        }

        recordTypeMapper.updateEntity(recordType, request);
        RecordType updatedRecordType = recordTypeRepository.save(recordType);
        return recordTypeMapper.toResponse(updatedRecordType);
    }

    @Transactional(readOnly = true)
    public RecordTypeResponse getRecordTypeByUuid(UUID recordTypeUuid) {
        RecordType recordType = recordTypeRepository.findByRecordTypeUuid(recordTypeUuid)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with UUID: " + recordTypeUuid));
        return recordTypeMapper.toResponse(recordType);
    }

    @Transactional(readOnly = true)
    public RecordTypeResponse getRecordTypeByCode(String typeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(typeCode)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with code: " + typeCode));
        return recordTypeMapper.toResponse(recordType);
    }

    @Transactional(readOnly = true)
    public Optional<RecordTypeResponse> findRecordTypeByUuid(UUID recordTypeUuid) {
        return recordTypeRepository.findByRecordTypeUuid(recordTypeUuid)
            .map(recordTypeMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Optional<RecordTypeResponse> findRecordTypeByCode(String typeCode) {
        return recordTypeRepository.findByTypeCode(typeCode)
            .map(recordTypeMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<RecordTypeResponse> getAllRecordTypes(Pageable pageable) {
        Page<RecordType> recordTypes = recordTypeRepository.findAll(pageable);
        List<RecordTypeResponse> responses = recordTypes.getContent().stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, recordTypes.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<RecordTypeResponse> getAllRecordTypes() {
        return recordTypeRepository.findAll().stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecordTypeResponse> getRootRecordTypes() {
        return recordTypeRepository.findByParentIsNull().stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecordTypeResponse> getChildRecordTypes(UUID parentUuid) {
        RecordType parent = recordTypeRepository.findByRecordTypeUuid(parentUuid)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with UUID: " + parentUuid));

        return recordTypeRepository.findByParent(parent).stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecordTypeResponse> getRecordTypesByName(String typeName) {
        return recordTypeRepository.findByTypeName(typeName).stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Page<RecordTypeResponse> searchRecordTypes(String keyword, Pageable pageable) {
        Page<RecordType> recordTypes = recordTypeRepository.searchByKeyword(keyword, pageable);
        List<RecordTypeResponse> responses = recordTypes.getContent().stream()
            .map(recordTypeMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, recordTypes.getTotalElements());
    }

    public void deleteRecordType(UUID recordTypeUuid) {
        RecordType recordType = recordTypeRepository.findByRecordTypeUuid(recordTypeUuid)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with UUID: " + recordTypeUuid));
        recordTypeRepository.delete(recordType);
    }

    @Transactional(readOnly = true)
    public boolean existsByUuid(UUID recordTypeUuid) {
        return recordTypeRepository.existsByRecordTypeUuid(recordTypeUuid);
    }

    @Transactional(readOnly = true)
    public boolean existsByTypeCode(String typeCode) {
        return recordTypeRepository.existsByTypeCode(typeCode);
    }

    @Transactional(readOnly = true)
    public long countRecordTypes() {
        return recordTypeRepository.count();
    }

    @Transactional(readOnly = true)
    public long countRootRecordTypes() {
        return recordTypeRepository.findByParentIsNull().size();
    }

    @Transactional(readOnly = true)
    public long countChildRecordTypes(UUID parentUuid) {
        RecordType parent = recordTypeRepository.findByRecordTypeUuid(parentUuid)
            .orElseThrow(() -> new RecordTypeNotFoundException(
                "Record type not found with UUID: " + parentUuid));
        return recordTypeRepository.findByParent(parent).size();
    }
}