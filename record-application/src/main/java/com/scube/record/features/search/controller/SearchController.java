package com.scube.record.features.search.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.features.search.dto.*;
import com.scube.record.features.search.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.List;

@RestController
@RequestMapping("/api/v1/search")
@RequiredArgsConstructor
@Tag(name = "Search", description = "Generic search operations across all record types")
public class SearchController {

    private final SearchService searchService;

    @PostMapping("/global")
    @Operation(summary = "Global search", description = "Search across all record types with a simple text query")
    public ResponseEntity<Page<SearchResult>> globalSearch(@RequestBody GlobalSearchRequest request) {
        Page<SearchResult> results = searchService.globalSearch(request);
        return ResponseEntity.ok(results);
    }

    @PostMapping("/advanced")
    @Operation(summary = "Advanced search", description = "Advanced search with detailed filters and criteria")
    public ResponseEntity<Page<SearchResult>> advancedSearch(@RequestBody AdvancedSearchRequest request) {
        Page<SearchResult> results = searchService.advancedSearch(request);
        return ResponseEntity.ok(results);
    }

    @PostMapping("/related")
    @Operation(summary = "Find related records", description = "Find records related to a specific record through associations")
    public ResponseEntity<Page<SearchResult>> findRelatedRecords(@RequestBody RelatedRecordsRequest request) {
        Page<SearchResult> results = searchService.findRelatedRecords(request);
        return ResponseEntity.ok(results);
    }

    @GetMapping("/suggestions")
    @Operation(summary = "Get record name suggestions", description = "Get autocomplete suggestions for record names")
    public ResponseEntity<List<String>> suggestRecordNames(
            @Parameter(description = "Search query") @RequestParam String query,
            @Parameter(description = "Record type code (optional)") @RequestParam(required = false) String recordTypeCode,
            @Parameter(description = "Maximum number of suggestions") @RequestParam(defaultValue = "10") int limit) {
        List<String> suggestions = searchService.suggestRecordNames(query, recordTypeCode, limit);
        return ResponseEntity.ok(suggestions);
    }

    // ========== NEW GLOBAL SEARCH ENDPOINT (formerly suggestions) ==========

    @GetMapping("/global")
    @Operation(summary = "Global search with record objects",
            description = "Search across all record types and return full record objects with exact match option")
    public ResponseEntity<List<SearchResult>> globalSearchRecords(
            @Parameter(description = "Search query") @RequestParam String query,
            @Parameter(description = "Exact match only") @RequestParam(defaultValue = "false") boolean exactMatch,
            @Parameter(description = "Record type code (optional)") @RequestParam(required = false) String recordTypeCode,
            @Parameter(description = "Maximum number of results") @RequestParam(defaultValue = "10") int limit) {

        List<SearchResult> results = searchService.globalSearchRecords(query, exactMatch, recordTypeCode, limit);
        return ResponseEntity.ok(results);
    }

    // ========== ADVANCED SEARCH AS GET ==========

    @GetMapping("/advanced")
    @Operation(summary = "Advanced search with GET",
            description = "Advanced search with detailed filters using GET request and query parameters")
    public ResponseEntity<Page<SearchResult>> advancedSearchGet(
            @Parameter(description = "Record type code") @RequestParam(required = false) String recordTypeCode,
            @Parameter(description = "Record status (comma-separated)") @RequestParam(required = false) String status,
            @Parameter(description = "Created after (ISO date)") @RequestParam(required = false) String createdAfter,
            @Parameter(description = "Created before (ISO date)") @RequestParam(required = false) String createdBefore,
            @Parameter(description = "Modified after (ISO date)") @RequestParam(required = false) String modifiedAfter,
            @Parameter(description = "Modified before (ISO date)") @RequestParam(required = false) String modifiedBefore,
            @Parameter(description = "Field filters (JSON string)") @RequestParam(required = false) String fieldFilters,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        // Convert comma-separated status to list
        List<String> statusList = status != null ? List.of(status.split(",")) : null;

        // Parse dates safely
        Instant createdAfterInstant = parseInstant(createdAfter);
        Instant createdBeforeInstant = parseInstant(createdBefore);
        Instant modifiedAfterInstant = parseInstant(modifiedAfter);
        Instant modifiedBeforeInstant = parseInstant(modifiedBefore);

        // Parse JSON filters
        JsonNode jsonFilters = null;
        if (fieldFilters != null && !fieldFilters.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                jsonFilters = mapper.readTree(fieldFilters);
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid fieldFilters JSON: " + e.getMessage());
            }
        }

        AdvancedSearchRequest request = new AdvancedSearchRequest(
                recordTypeCode,
                null, // fieldFilters
                jsonFilters,
                statusList,
                createdAfterInstant,
                createdBeforeInstant,
                modifiedAfterInstant,
                modifiedBeforeInstant,
                null, // createdBy
                null, // lastModifiedBy
                "createdAt",
                "DESC",
                page,
                size
        );
        Page<SearchResult> results = searchService.advancedSearch(request);
        return ResponseEntity.ok(results);
    }

    private Instant parseInstant(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        try {
            return Instant.parse(dateString);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + dateString + ". Expected ISO format.", e);
        }
    }
}