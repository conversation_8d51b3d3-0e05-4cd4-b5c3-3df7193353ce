server:
  port: 9009
  servlet:
    context-path: /api/document-template
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always
    include-exception: true
    include-client-error-message: true
    include-server-error-message: true
    send-client-error-email: false
    send-server-error-email: false
  forward-headers-strategy: framework


logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    com.scube.rabbit: "warn"
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "info"

spring:
  application:
    name: DocumentTemplateService
  threads:
    virtual:
      enabled: false

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        format_sql: true



springdoc:
  show-actuator: false
  swagger-ui:
    filter: true


keycloak:
  host: http://keycloak.keycloak.svc.cluster.local:8080
  public-host: https://auth-dev.clerkxpress.com
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.public-host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.host}
      jwkSetUri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.public-host}
      jwk-set-uri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
      - "/webhook/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - https://dev.clerkxpress.com
          - https://swagger-dev.clerkxpress.com
          - http://localhost:3000
          - https://localhost:3000

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  scheduling:
    enabled: true
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "jdbc:postgresql://*************:5432/postgres?currentSchema=document_templates"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

document-template-helper:
  url: http://scube-document-template-helper-service-srv:9012/api/document-template-helper
  maxInMemorySize: 200MB

write-template-to-disk: false

file-converter:
  type: gotenberg
  gotenberg:
    url: http://gotenberg.gotenberg.svc.cluster.local
    maxInMemorySize: 200MB
    webhookUrl: http://scube-document-template-service-srv.backend.svc.cluster.local:9009/api/document-template/webhook/gotenberg
    webhookErrorUrl: http://scube-document-template-service-srv.backend.svc.cluster.local:9009/api/document-template/webhook/gotenberg/error

scheduling:
  enabled: true

com.scube.client:
  auth: "http://scube-auth-service-srv:9001/api/auth"
  document: "http://scube-document-service-srv:9003/api/document-service"
