package com.scube.documenttemplates.strategy.template_builder;

import com.scube.documenttemplates.dto.FillFileTemplateRequest;
import com.scube.documenttemplates.model.Template;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import com.scube.documenttemplates.repository.TemplateRepository;
import com.scube.documenttemplates.service.FileService;
import com.scube.documenttemplates.service.TemplatingService;
import com.scube.documenttemplates.utils.TemplateExtensionMapping;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Slf4j
@Service("xlsxTemplateBuilder")
@RequiredArgsConstructor
public class ExcelTemplateBuilder implements ITemplateBuilder {
    private final TemplateRepository templateRepository;
    private final FileService fileService;
    private final TemplatingService templatingService;
    private final AmqpGateway amqpGateway;

    @Transactional
    public Template getTemplate(String templateNameKey) {
        return templateRepository.findByNameKey(templateNameKey + "Excel")
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Template with nameKey: " + templateNameKey + " not found"));
    }

    @Override
    public UUID fillFileTemplate(String data, String nameKey) {
        log.info("fillFileTemplate");
        Template template = getTemplate(nameKey);

        Resource templateFileBytes = fileService.getTemplateFileResponse(template).getBody();
        if (ObjectUtils.isEmpty(templateFileBytes)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template file is empty");
        }

        var xlsBytes = templatingService.processExcelTemplate(data, templateFileBytes);

        var contentType = fileService.determineContentType(String.valueOf(TemplateExtensionMapping.XLSX));
        var fileResponse = fileService.saveFilledTemplateFile(xlsBytes, template.getNameKey() + "." + TemplateExtensionMapping.XLSX, contentType);
        return fileResponse.getDocumentUUID();
    }

    @Override
    public void fillFileTemplateAsync(FillFileTemplateRequest request) {
        var startTime = System.currentTimeMillis();
        var documentId = fillFileTemplate(request.getDataAsString(), request.getNameKey());

        amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedEvent(
                request.getParentId(), request.getParentType(), request.getGroupId(), documentId.toString())
        );
        log.info("Document generated in {} ms", System.currentTimeMillis() - startTime);
    }
}
