package com.scube.documenttemplates.strategy.template_builder;

import com.scube.documenttemplates.dto.FillFileTemplateRequest;
import com.scube.documenttemplates.model.Template;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import com.scube.documenttemplates.repository.TemplateRepository;
import com.scube.documenttemplates.service.FileService;
import com.scube.documenttemplates.service.TemplatingService;
import com.scube.documenttemplates.utils.TemplateExtensionMapping;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.UUID;

@Slf4j
@Service("docxTemplateBuilder")
@RequiredArgsConstructor
public class DocxTemplateBuilder implements ITemplateBuilder {
    private final FileService fileService;
    private final TemplateRepository templateRepository;
    private final TemplatingService templatingService;
    private final AmqpGateway amqpGateway;

    @Override
    public UUID fillFileTemplate(String data, String templateNameKey) {
        Template template = templateRepository.findByNameKey(templateNameKey)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Template with nameKey: " + templateNameKey + " not found"));

        byte[] templateFileBytes = fileService.getTemplateFile(template);

        if (ObjectUtils.isEmpty(templateFileBytes)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template file is empty");
        }

        var contentType = fileService.determineContentType(String.valueOf(TemplateExtensionMapping.DOCX));
        var docxBytes = templatingService.processWordTemplate(data, templateFileBytes);

        var fileResponse = fileService.saveFilledTemplateFile(docxBytes, template.getNameKey() + "." + TemplateExtensionMapping.DOCX, contentType);
        return fileResponse.getDocumentUUID();
    }

    @Override
    public void fillFileTemplateAsync(FillFileTemplateRequest request) {
        var startTime = System.currentTimeMillis();
        var templateNameKey = request.getNameKey();
        Template template = templateRepository.findByNameKey(templateNameKey)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Template with nameKey: " + templateNameKey + " not found"));

        byte[] templateFileBytes = fileService.getTemplateFile(template);

        if (ObjectUtils.isEmpty(templateFileBytes)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template file is empty");
        }

        var contentType = fileService.determineContentType(String.valueOf(TemplateExtensionMapping.DOCX));
        var springContext = SecurityContextHolder.getContext();
        templatingService.processWordTemplateAsync(request.getDataAsString(), templateFileBytes)
                .retryWhen(
                        Retry.backoff(20, Duration.ofSeconds(10))
                                .maxBackoff(Duration.ofMinutes(1))
                                .jitter(0.5)
                                .doBeforeRetry(retrySignal ->
                                        log.warn("Retrying document generation (attempt {}/{}) due to error: {}",
                                                retrySignal.totalRetries() + 1,
                                                20,
                                                retrySignal.failure().getMessage()))
                )
                .flatMap(docxBytes -> {
                    SecurityContextHolder.setContext(springContext);
                    return fileService.saveFilledTemplateFileAsync(
                            docxBytes,
                            template.getNameKey() + "." + TemplateExtensionMapping.DOCX,
                            contentType
                    );
                })
                .subscribe(fileResponse -> {
                    SecurityContextHolder.setContext(springContext);
                    log.info("Document generated successfully: {}", fileResponse.getDocumentUUID());
                    amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedEvent(
                            request.getParentId(), request.getParentType(), request.getGroupId(), fileResponse.getDocumentUUID().toString())
                    );
                    log.info("Document generated in {} ms", System.currentTimeMillis() - startTime);
                }, error -> {
                    SecurityContextHolder.setContext(springContext);
                    log.error("Error generating document: ", error);
                    amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(
                            request.getParentId(), request.getParentType(), request.getGroupId(), error.getMessage())
                    );
                });
    }
}