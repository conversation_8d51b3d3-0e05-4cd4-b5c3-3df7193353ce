package com.scube.documenttemplates.rabbit;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.documenttemplates.dto.FillFileTemplateRequest;
import com.scube.documenttemplates.strategy.template_builder.TemplateBuilderFactory;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateDocumentCommandHandler extends FanoutListener<GenerateDocumentCommandHandler.GenerateDocumentCommand> {
    private final AmqpGateway amqpGateway;
    private final TemplateBuilderFactory templateBuilderFactory;

    public void consume(GenerateDocumentCommand command) {
        try {
            log.debug("GenerateDocumentCommandHandler consume: parentId={}, parentType={}, nameKey={}, format={}",
                    command.getParentId(), command.getParentType(), command.getNameKey(), command.getFormat());
            var fillRequest = new FillFileTemplateRequest(command);
            templateBuilderFactory.fillFileTemplateAsync(fillRequest);
        } catch (Exception e) {
            amqpGateway.publish(new DocumentGeneratedErrorEvent(
                    command.getParentId(),
                    command.getParentType(),
                    command.getGroupId(),
                    e.getMessage())
            );
            throw e;
        }
    }

    @Data
    public static class GenerateDocumentCommand implements IRabbitFanoutSubscriber {
        private String parentId;
        private String parentType;
        private String groupId;
        private String nameKey;
        private String format = "PDF";
        private JsonNode data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentGeneratedEvent implements IRabbitFanoutPublisher {
        private String parentId;
        private String parentType;
        private String groupId;
        private String documentId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentGeneratedErrorEvent implements IRabbitFanoutPublisher {
        private String parentId;
        private String parentType;
        private String groupId;
        private String error;
    }
}