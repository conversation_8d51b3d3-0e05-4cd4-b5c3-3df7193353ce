package com.scube.documenttemplates.service.file_converter.gotenberg;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.auth.library.ITokenService;
import com.scube.documenttemplates.CustomMultipartFile;
import com.scube.documenttemplates.service.file_converter.IFileConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "file-converter.type", havingValue = "gotenberg")
public class GotenbergFileConverter implements IFileConverter {
    private final GotenbergHttpExchange gotenBergHttpExchange;
    private final GotenbergFileConverterProperties properties;
    private final ITokenService tokenService;
    private final ObjectMapper objectMapper;

    @Override
    public byte[] convertDocxToPdf(byte[] docBytes) {
        try {
            log.info("GotenbergFileConverter.convertDocxToPdf converting document to pdf");
            var gotenbergFiles = toGotenbergFile(docBytes);
            return gotenBergHttpExchange.convertDocument(gotenbergFiles);
        } catch (Exception e) {
            log.error("Error converting document to pdf", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error converting document to pdf", e);
        }
    }

    private static GotenbergFiles toGotenbergFile(byte[] docBytes) {
        var mpfile = new CustomMultipartFile("files.docx", "files.docx", MediaType.APPLICATION_OCTET_STREAM_VALUE, docBytes);
        var gotenbergFiles = GotenbergFiles.of(mpfile);
        return gotenbergFiles;
    }

    private Mono<String> getTokenAsync() {
        var context = SecurityContextHolder.getContext();
        return Mono.fromCallable(() -> {
                    SecurityContextHolder.setContext(context);
                    return tokenService.getNewAccessTokenFromCurrentRealm();
                })
                .subscribeOn(Schedulers.boundedElastic());
    }

    @Override
    public Mono<Void> convertDocxToPdfAsync(byte[] docBytes, String parentType, String parentId, String groupId) {
        try {
            log.info("GotenbergFileConverter.convertDocxToPdfAsync converting document to pdf - parentType: {}, parentId: {}, groupId: {}",
                    parentType, parentId, groupId);
            var gotenbergFiles = toGotenbergFile(docBytes);
            var context = SecurityContextHolder.getContext();
            return getTokenAsync()
                    .flatMap(token -> {
                        SecurityContextHolder.setContext(context);
                        var headers = new java.util.HashMap<String, String>();
                        if (token != null) {
                            headers.put("Authorization", token);
                        }
                        if (parentType != null) {
                            headers.put("Parent-Type", parentType);
                        }
                        if (parentId != null) {
                            headers.put("Parent-Id", parentId);
                        }
                        if (groupId != null) {
                            headers.put("Group-Id", groupId);
                        }
                        String headersJson = null;
                        try {
                            headersJson = objectMapper.writeValueAsString(headers);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        return gotenBergHttpExchange.convertDocumentAsync(gotenbergFiles, properties.getWebhookUrl(),
                                properties.getWebhookErrorUrl(), headersJson);
                    });
        } catch (Exception e) {
            log.error("Error converting document to pdf", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error converting document to pdf", e);
        }
    }

    @Override
    public byte[] convert(String fromFormat, String toFormat, byte[] docBytes) {
        return new byte[0];
    }
}
