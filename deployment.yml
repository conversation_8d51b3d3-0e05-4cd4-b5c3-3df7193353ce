apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-document-template-service-depl
  labels:
    app: scube-document-template-service
spec:
  selector:
    matchLabels:
      app: scube-document-template-service
  template:
    metadata:
      labels:
        app: scube-document-template-service
    spec:
      nodeSelector:
        subnet-type: private
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-document-template-service
          image: service_document_template
          ports:
            - containerPort: 9009
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-document-template-service-srv.backend.svc.cluster.local
              path: /api/document-template/actuator/health
              port: 9009
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9009
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-id-secret
                  key: swagger-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-secret
            - name: ENCRYPTION_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: encryption-secret
                  key: secret-Key
---
apiVersion: v1
kind: Service
metadata:
  name: scube-document-template-service-srv
  labels:
    app: scube-document-template-service
spec:
  selector:
    app: scube-document-template-service
  type: ClusterIP
  ports:
    - name: template-port
      protocol: TCP
      port: 9009
      targetPort: 9009
